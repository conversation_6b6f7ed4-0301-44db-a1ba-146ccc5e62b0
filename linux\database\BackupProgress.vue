<template>
  <view class="backup-progress-container">
    <view class="backup-header pt-20">
      <view class="flex items-center gap-2">
        <uv-loading-icon 
          v-if="isBackingUp" 
          mode="spinner" 
          size="20" 
          color="#28a745"
        ></uv-loading-icon>
        <uni-icons 
          v-else 
          type="checkmarkempty" 
          size="20" 
          color="#28a745"
        ></uni-icons>
        <text class="backup-title">
          {{ isBackingUp ? '正在备份数据库' : '数据库备份完成' }}
        </text>
      </view>
      <text class="backup-subtitle">
        当前备份任务为后台备份，可关闭此页面进行其他操作。
      </text>
    </view>

    <view class="backup-info">
      <view class="info-grid">
        <view class="info-item">
          <text class="info-label">备份数据库:</text> 
          <text class="info-value">{{ databaseName || '--' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">备份大小:</text> 
          <text class="info-value">{{ backupInfo.total || '--' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">备份速度:</text> 
          <text class="info-value">{{ backupInfo.speed || '--' }}</text>
        </view>
      </view>
    </view>

    <view class="log-container">
      <scroll-view 
        class="log-scroll" 
        scroll-y 
        :scroll-into-view="scrollToId"
      >
        <text 
          v-if="!logContent" 
          class="log-placeholder"
        >获取中...</text>
        <text 
          v-else
          :id="'log-end'"
          class="log-content"
        >{{ logContent }}</text>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, nextTick, onUnmounted } from 'vue'
import { getBackupLog } from '@/api/database'

const props = defineProps({
  databaseId: {
    type: [String, Number],
    default: null
  },
  databaseName: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const isBackingUp = ref(true)
const logContent = ref('')
const scrollToId = ref('')
const backupInfo = ref({
  total: '--',
  speed: '--'
})

let logTimer = null

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    startBackupProcess()
  } else {
    stopLogPolling()
  }
})

// 开始备份流程
const startBackupProcess = () => {
  isBackingUp.value = true
  logContent.value = ''
  backupInfo.value = {
    total: '--',
    speed: '--'
  }
  
  // 开始轮询获取日志
  startLogPolling()
}

// 开始轮询日志
const startLogPolling = () => {
  if (!props.databaseId) {
    console.log('BackupProgress: databaseId 为空，无法开始轮询')
    return
  }

  console.log('BackupProgress: 开始轮询日志，databaseId:', props.databaseId)

  const pollLog = async () => {
    try {
      console.log('BackupProgress: 调用 getBackupLog API，参数:', { id: props.databaseId })
      const res = await getBackupLog({ id: props.databaseId })
      console.log('BackupProgress: API 响应:', res)

      if (res && res.status) {
        logContent.value = res.msg || ''
        backupInfo.value = {
          total: res.total || '--',
          speed: res.speed || '--'
        }

        console.log('BackupProgress: 更新日志内容:', res.msg)

        // 滚动到底部
        nextTick(() => {
          scrollToId.value = 'log-end'
        })

        // 检查是否备份完成
        if (res.msg && (res.msg.includes('successful') || res.msg.includes('完成'))) {
          console.log('BackupProgress: 备份完成，停止轮询')
          isBackingUp.value = false
          stopLogPolling()
        }
      } else {
        console.log('BackupProgress: API 返回状态为 false 或响应为空:', res)
        logContent.value = res?.msg || '获取日志失败'
      }
    } catch (error) {
      console.error('BackupProgress: 获取备份日志失败:', error)
      logContent.value = '获取日志时发生错误: ' + (error.message || error)
    }
  }

  // 立即执行一次
  pollLog()

  // 每2秒轮询一次
  logTimer = setInterval(pollLog, 2000)
}

// 停止轮询
const stopLogPolling = () => {
  if (logTimer) {
    clearInterval(logTimer)
    logTimer = null
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopLogPolling()
})
</script>

<style lang="scss" scoped>
.backup-progress-container {
  padding: 0;
}

.backup-header {
  padding-bottom: 24rpx;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 24rpx;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-2 {
  gap: 16rpx;
}

.backup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary);
}

.backup-subtitle {
  font-size: 24rpx;
  color: var(--text-color-tertiary);
  margin-top: 8rpx;
}

.backup-info {
  margin-bottom: 24rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color-secondary);
  margin-right: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: var(--text-color-primary);
}

.log-container {
  background-color: #1e293b;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 300rpx;
}

.log-scroll {
  height: 300rpx;
  font-family: 'Courier New', Courier, monospace;
}

.log-placeholder {
  color: #94a3b8;
  font-size: 24rpx;
}

.log-content {
  color: #e2e8f0;
  font-size: 24rpx;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', Courier, monospace;
}

@media (min-width: 640px) {
  .info-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
