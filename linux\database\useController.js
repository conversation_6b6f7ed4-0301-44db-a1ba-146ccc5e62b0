import { ref } from 'vue';
import { getSoftStatus } from '@/api/config';
import { getMysqlList, getMysqlCloudServer, deleteDatabase, updateRemark } from '@/api/database';
import { triggerVibrate } from '@/utils/common';
import { $t } from '@/locale';

export const pageContainer = ref(null);

// 数据库位置
export const sqlLocationList = ref([]);

// 数据列表
export const sqlList = ref([]);

// 分页相关
export const databasePaging = ref(null);
export const databaseList = ref([]);

// 上下文菜单相关
export const showContextMenu = ref(false);
export const activeDatabase = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 对话框相关
export const showEditDialog = ref(false);
export const showDeleteDialog = ref(false);
export const editRemark = ref('');

// 触摸相关
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 安装状态
export const isInstallDatabase = ref(true);

// 获取数据库状态
export const getDataBaseStatus = async () => {
	try {
		const res = await getSoftStatus({ name: 'mysql' });
		isInstallDatabase.value = res.s_status;
	} catch (error) {
		console.error(error);
	}
};

export const getServerList = async () => {
	try {
		const res = await getMysqlCloudServer();
		sqlLocationList.value = res;
	} catch (error) {
		console.error(error);
	}
};

export const getSqlList = async (page, pageSize) => {
	try {
		const res = await getMysqlList({
			p: page,
			limit: pageSize,
			table: 'databases',
		});
		return res.data;
	} catch (e) {
		console.log(e);
	}
};

// 分页查询
export const queryList = async (page, pageSize) => {
	try {
		const res = await getSqlList(page, pageSize);
		databasePaging.value.complete(res);
		databasePaging.value.updateVirtualListRender();
	} catch (error) {
		databasePaging.value.complete([]);
		console.error(error);
	}
};

export const virtualListChange = (vList) => {
	databaseList.value = vList;
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const dbData = JSON.parse(event.currentTarget.dataset.db);
			showFloatingMenu(dbData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开详情
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const dbData = JSON.parse(event.currentTarget.dataset.db);
		// handleDatabaseDetail(dbData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单
export const showFloatingMenu = (database, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeDatabase.value = database;
	activeIndex.value = index;

	// 获取被长按元素的位置
	uni.createSelectorQuery()
		.selectAll('.db-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync();
			const screenHeight = systemInfo.windowHeight;
			const screenWidth = systemInfo.windowWidth;

			// 预估参数
			const menuHeight = 175; // 菜单高度估计值

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示
			menuLeft = rect.left + rect.width / 2;

			// 垂直定位 - 智能判断上方还是下方
			const spaceBelow = screenHeight - rect.bottom;
			const spaceAbove = rect.top;

			if (spaceBelow >= menuHeight) {
				// 下方有足够空间
				menuTop = rect.bottom + 5;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= menuHeight) {
				// 上方有足够空间
				menuTop = rect.top - menuHeight;
				menuClass = 'menu-top';
			} else {
				// 选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					menuTop = rect.bottom + 5;
					menuClass = 'menu-bottom';
				} else {
					menuTop = rect.top - menuHeight;
					menuClass = 'menu-top';
				}
			}

			// 设置菜单位置
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeDatabase.value = null;
	activeIndex.value = -1;
};

// 复制密码
export const copyPassword = () => {
	if (!activeDatabase.value) return;
	if (!activeDatabase.value.password) {
		pageContainer.value.notify.error($t('database.noPasswordSet'));
		return;
	}

	uni.setClipboardData({
		data: activeDatabase.value.password,
		success: () => {
			uni.showToast({
				title: $t('database.passwordCopied'),
				icon: 'success',
			});
			hideContextMenu();
		},
	});
};

// 确认修改备注
export const confirmEdit = async (close) => {
	if (!editRemark.value.trim() || !activeDatabase.value) {
		return;
	}

	const res = await updateRemark({
		id: activeDatabase.value.id,
		table: 'databases',
		ps: editRemark.value,
	});
	if (res.status) {
		// 关闭对话框并重置
		close && close();
		editRemark.value = '';
		hideContextMenu();
		databasePaging.value.reload();
		pageContainer.value.notify.success(res.msg);
	} else {
		pageContainer.value.notify.error(res.msg);
	}
};

// 确认删除
export const confirmDelete = async (close) => {
	if (!activeDatabase.value) return;
	const res = await deleteDatabase({
		id: activeDatabase.value.id,
		name: activeDatabase.value.name,
	});
	if (res.status) {
		// 关闭对话框
		close && close();
		databasePaging.value.reload();
		pageContainer.value.notify.success(res.msg);
		setTimeout(() => {
			hideContextMenu();
		}, 200);
	} else {
		pageContainer.value.notify.error(res.msg);
	}
};

export const getPosition = (list, type, sid) => {
	let position = $t('database.localDatabase');
	list?.forEach((item) => {
		if (item.id === sid) {
			position = type === 'mysql' ? item.db_host : item.ps;
			if (item.db_host === '127.0.0.1') position = $t('database.localDatabase');
		}
	});
	return position;
};
